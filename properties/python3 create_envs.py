import requests
import os

# Configuración de tu repositorio y credenciales
BITBUCKET_WORKSPACE = "git-m4u"
BITBUCKET_REPO = "ws-mailer"
USERNAME = "oscar"
APP_PASSWORD = "TU_APP_PASSWORD"  # Crear desde Bitbucket > Personal settings > App passwords

BITBUCKET_API = f"https://api.bitbucket.org/2.0/repositories/{BITBUCKET_WORKSPACE}/{BITBUCKET_REPO}"

# Environments que quieras crear
environments = ["develop", "qa", "production"]

# Leer el archivo properties
def load_properties(filepath):
    props = {}
    with open(filepath, "r") as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                key, value = line.split("=", 1)
                props[key.strip()] = value.strip()
    return props

# Crear un environment en Bitbucket
def create_environment(env_name):
    url = f"{BITBUCKET_API}/environments/"
    payload = {
        "type": "deployment_environment",
        "name": env_name,
        "environment_type": env_name  # puede ser: Test, Staging, Production
    }
    r = requests.post(url, auth=(USERNAME, APP_PASSWORD), json=payload)
    if r.status_code in (200, 201):
        print(f"✅ Environment '{env_name}' creado")
    else:
        print(f"⚠️ Error creando {env_name}: {r.text}")

# Crear variables en un environment
def create_variables(env_name, variables):
    # Primero obtener el UUID del environment
    envs = requests.get(f"{BITBUCKET_API}/environments/", auth=(USERNAME, APP_PASSWORD)).json()
    env_uuid = None
    for env in envs.get("values", []):
        if env["name"].lower() == env_name.lower():
            env_uuid = env["uuid"]
            break
    if not env_uuid:
        print(f"⚠️ No encontré el environment {env_name}")
        return

    url = f"{BITBUCKET_API}/deployments_config/environments/{env_uuid}/variables/"
    for key, value in variables.items():
        payload = {"key": key, "value": value, "secured": True}
        r = requests.post(url, auth=(USERNAME, APP_PASSWORD), json=payload)
        if r.status_code in (200, 201):
            print(f"   ➕ Variable {key} creada en {env_name}")
        else:
            print(f"   ⚠️ Error creando {key}: {r.text}")


if __name__ == "__main__":
    variables = load_properties("deployment.properties")

    for env in environments:
        create_environment(env)
        create_variables(env, variables)
