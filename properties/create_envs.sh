#!/bin/bash

# 🔧 Configuración
WORKSPACE="git-m4u"
REPO="ws-mailer"

# ⚠️ Aquí pones tu correo y el API Token de Bitbucket directamente
BB_EMAIL="<EMAIL>"
BB_TOKEN="ATATT3xFfGF0F7IOOFZJslKCe8Vo4YIghhs7LdPNTKzaumW2aggj4NQoVO0PUzl8qZ7mh6Foiik1ZsPP6N02-C6bbnpD-on9bc5bIggB_eH_2URd1JdgReUIuuj2wqo44ensU3d6GS15y_snik3aH2h8tZV8eBOnICxqnoL-DJNpyDktdAuQC64=18DAC210"

# Ambientes que quieres crear
ENVIRONMENTS=("develop" "qa" "production")

# Archivo con las variables
PROPERTIES_FILE="deployment.properties"

# Crear un environment
create_environment() {
  local env_name=$1

  echo "📦 Creando environment: $env_name en $WORKSPACE/$REPO"

  curl -s -X POST \
    -u "$BB_EMAIL:$BB_TOKEN" \
    -H "Content-Type: application/json" \
    "https://api.bitbucket.org/2.0/repositories/$WORKSPACE/$REPO/environments/" \
    -d "{
      \"type\": \"deployment_environment\",
      \"name\": \"$env_name\",
      \"environment_type\": \"$env_name\"
    }"
}

# Obtener UUID de un environment
get_env_uuid() {
  local env_name=$1
  curl -s \
    -u "$BB_EMAIL:$BB_TOKEN" \
    "https://api.bitbucket.org/2.0/repositories/$WORKSPACE/$REPO/environments/" \
  | jq -r ".values[] | select(.name==\"$env_name\") | .uuid"
}

# Crear variables en un environment
create_variables() {
  local env_name=$1
  local env_uuid=$2

  echo "   🔑 Cargando variables en el ambiente '$env_name' (UUID: $env_uuid)"

  while IFS='=' read -r key value; do
    if [[ -z "$key" || "$key" =~ ^# ]]; then
      continue
    fi
    key=$(echo "$key" | xargs)
    value=$(echo "$value" | xargs)

    echo "      ➕ Creando variable $key"
    curl -s -X POST \
      -u "$BB_EMAIL:$BB_TOKEN" \
      -H "Content-Type: application/json" \
      "https://api.bitbucket.org/2.0/repositories/$WORKSPACE/$REPO/deployments_config/environments/$env_uuid/variables/" \
      -d "{
        \"key\": \"$key\",
        \"value\": \"$value\",
        \"secured\": true
      }"
  done < "$PROPERTIES_FILE"
}

# 🚀 Proceso principal
for env in "${ENVIRONMENTS[@]}"; do
  create_environment "$env"
  env_uuid=$(get_env_uuid "$env")
  if [[ -n "$env_uuid" ]]; then
    create_variables "$env" "$env_uuid"
  else
    echo "⚠️ No encontré el UUID para el ambiente $env"
  fi
done

echo "✅ Proceso completo para $WORKSPACE/$REPO"
